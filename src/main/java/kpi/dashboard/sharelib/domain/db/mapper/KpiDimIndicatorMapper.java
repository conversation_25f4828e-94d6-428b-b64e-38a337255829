package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.KpiDimIndicator;
import kpi.dashboard.sharelib.domain.db.model.KpiIndicator;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface KpiDimIndicatorMapper extends BaseMapper<KpiDimIndicator> {

    @Insert("INSERT IGNORE INTO kpi_dim_indicator(id,dim_name,dim_code,init_data_sql,sort,is_enabled,remark,creator,updater)" +
            " VALUES(#{id},#{dimName},#{dimCode},#{initDataSql},#{sort},#{isEnabled},#{remark},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(KpiDimIndicator kpiDimIndicator);

    @Insert("INSERT INTO kpi_dim_indicator(id,dim_name,dim_code,init_data_sql,sort,is_enabled,remark,creator,updater)" +
            " VALUES(#{id},#{dimName},#{dimCode},#{initDataSql},#{sort},#{isEnabled},#{remark},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE dim_name=VALUES(dim_name),dim_code=VALUES(dim_code),init_data_sql=VALUES(init_data_sql),sort=VALUES(sort),is_enabled=VALUES(is_enabled),remark=VALUES(remark),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(KpiDimIndicator kpiDimIndicator);

    @Update("UPDATE kpi_dim_indicator set dim_name=#{dimName},dim_code=#{dimCode},init_data_sql=#{initDataSql},sort=#{sort},is_enabled=#{isEnabled},remark=#{remark},updater=#{updater} WHERE id=#{id}")
    int updateByEntity(KpiDimIndicator kpiDimIndicator);

    @Delete("DELETE FROM kpi_dim_indicator WHERE id=#{id}")
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update kpi_dim_indicator set is_deleted=true, updater=#{userKey} WHERE id=#{id}")
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,dim_name,dim_code,init_data_sql,sort,is_enabled,remark,creator,updater,create_time,update_time FROM kpi_dim_indicator WHERE id=#{id} ")
    @Results(id = "kpiDimIndicator-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "dimName", column = "dim_name"),
            @Result(property = "dimCode", column = "dim_code"),
            @Result(property = "initDataSql", column = "init_data_sql"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KpiDimIndicator getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,dim_name,dim_code,init_data_sql,sort,is_enabled,remark,creator,updater,create_time,update_time FROM kpi_dim_indicator WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "kpiDimIndicator-mapping")
    KpiDimIndicator getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys


    //update status sqls
    @Update("update kpi_dim_indicator set is_enabled=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsEnabled(@Param("id") Integer id, @Param("input") Boolean input, @Param("updater") String updater);

    @Select("SELECT id,dim_name,dim_code,init_data_sql,sort,is_enabled,remark,creator,updater,create_time,update_time FROM kpi_dim_indicator WHERE dim_code = #{dimCode} AND is_enabled = true")
    KpiDimIndicator getKpiDimIndicatorByDimCode(@Param("dimCode") String dimCode);

    @Select({
            "<script>",
            "SELECT id,dim_name,dim_code FROM kpi_dim_indicator",
            "WHERE is_enabled = true AND dim_name IN",
            "<foreach item='item' index='index' collection='codes' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>" +
            "ORDER BY sort ASC",
            "</script>"
    })
    List<KpiDimIndicator> findByCodes(@Param("codes") List<String> codes);

//get data by foreign keys

}
